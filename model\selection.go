package model

import "github.com/Norray/xrocket/xmodel"

const (
	// Facility Register
	SelectionTypeFacilityProfileFacilityType = "FACILITY_PROFILE_FACILITY_TYPE" // 機構類型

	// Personal Information
	SelectionTypeProfessionalPermissionToWork = "PROFESSIONAL_PERMISSION_TO_WORK" // 工作許可
	SelectionTypeProfessionalProfession       = "PROFESSIONAL_PROFESSION"         // 專業
	SelectionTypeGender                       = "GENDER"                          // 性別
	SelectionTypeRelationship                 = "RELATIONSHIP"                    // 關係

	// Work Preferences & Experience
	SelectionTypePreferredGrade                     = "PREFERRED_GRADE"                       // 首選級別
	SelectionTypeExperienceLevelMedicalPractitioner = "EXPERIENCE_LEVEL_MEDICAL_PRACTITIONER" // 經驗級別 - 執業醫生
	SelectionTypeExperienceLevelRegisteredNurse     = "EXPERIENCE_LEVEL_REGISTERED_NURSE"     // 經驗級別 - 註冊護士
	SelectionTypeExperienceLevelGeneral             = "EXPERIENCE_LEVEL_GENERAL"              // 經驗級別 - 通用年限
	SelectionTypeSupervisionRequirement             = "SUPERVISION_REQUIREMENT"               // 監督要求

	SelectionTypePreferredSpecialityMedicalPractitioner = "PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER" // 專業 - 執業醫生
	SelectionTypePreferredSpecialityNurse               = "PREFERRED_SPECIALTY_NURSE"                // 專業 - 護士
	SelectionTypePreferredSpecialityEnrolledNurse       = "PREFERRED_SPECIALTY_ENROLLED_NURSE"       // 專業 - 登記護士
	SelectionTypePreferredSpecialityRegisteredNurse     = "PREFERRED_SPECIALTY_REGISTERED_NURSE"     // 專業 - 註冊護士
	SelectionTypePreferredSpecialityPersonalCareWorker  = "PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER" // 專業 - 護理員

	SelectionTypeProfessionalPersonalCareWorkerQualification = "PROFESSIONAL_PERSONAL_CARE_WORKER_QUALIFICATION" // 護理員資格證書類型

	// Additional Certification
	SelectionTypeProfessionalDisclosureQuestion = "PROFESSIONAL_DISCLOSURE_QUESTION" // 專業人士披露問題

	// ID Check Files
	SelectionTypeProfessionalIdCheckFileGroupPrimary   = "PROFESSIONAL_ID_CHECK_FILE_GROUP_PRIMARY"   // 專業人士身份檢查文件組 - 主要
	SelectionTypeProfessionalIdCheckFileGroupSecondary = "PROFESSIONAL_ID_CHECK_FILE_GROUP_SECONDARY" // 專業人士身份檢查文件組 - 次要
	SelectionTypeProfessionalIdCheckFileGroupOthers    = "PROFESSIONAL_ID_CHECK_FILE_GROUP_OTHERS"    // 專業人士身份檢查文件組 - 其他

	// Languages
	SelectionTypeLanguage = "LANGUAGE" // 語言
)

const (
	SelectionStatusEnable  = "ENABLE"  // 啟用
	SelectionStatusDisable = "DISABLE" // 禁用

	SelectionHiddenYes = "Y" // 即使記錄已生成，也隱藏
	SelectionHiddenNo  = "N" // 不隱藏
)

// 選項主檔案
type Selection struct {
	Id            uint64 `json:"id" gorm:"primary_key"`
	SelectionType string `json:"selectionType" gorm:"type:varchar(64);index:selection_type_idx;not null"` // 類型
	Code          string `json:"code" gorm:"type:varchar(64);unique_index:code_idx;not null"`             // 編號
	Name          string `json:"name" gorm:"type:varchar(512);not null"`                                  // 名稱
	Seq           int32  `json:"seq" gorm:"not null"`                                                     // 排序
	Status        string `json:"status" gorm:"type:varchar(32);not null"`                                 // 狀態 ENABLE=啟用，DISABLE=禁用
	Hidden        string `json:"hidden" gorm:"type:varchar(1);not null"`                                  // 是否隱藏 Y=即使記錄已生成，也隱藏，N=不隱藏
	xmodel.Model
}

func (Selection) TableName() string {
	return "selection"
}

func (Selection) SwaggerDescription() string {
	return "選項主檔案"
}

// 專業人士的專業類型，數據庫添加SelectionType注意同步修改，用於查詢專業人士選項時過濾
var ProfessionalSelectionTypeMaps = map[string]bool{
	// "PREFERRED_SPECIALTY_MEDICAL_PRACTITIONER": true, // 注意：只保留最後一級的SelectionType
	"PSMP_MEDICINE":                            true,
	"PSMP_ANAESTHETICS":                        true,
	"PSMP_SURGERY":                             true,
	"PSMP_PATHOLOGY":                           true,
	"PSMP_PSYCHIATRY":                          true,
	"PSMP_RADIOLOGY":                           true,
	"PSMP_PAEDIATRICS":                         true,
	"PSMP_EMERGENCY_MEDICINE":                  true,
	"PSMP_GENERAL_PRACTICE":                    true,
	"PSMP_OBSTETRICS_AND_GYNAECOLOGY":          true,
	"PSMP_OPHTHALMOLOGY":                       true,
	"PSMP_PUBLIC_HEALTH_MEDICINE":              true,
	"PREFERRED_SPECIALTY_ENROLLED_NURSE":       true,
	"PREFERRED_SPECIALTY_REGISTERED_NURSE":     true,
	"PREFERRED_SPECIALTY_PERSONAL_CARE_WORKER": true,
}
